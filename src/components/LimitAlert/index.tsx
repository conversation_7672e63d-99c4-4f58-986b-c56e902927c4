import React, { useEffect, useMemo, useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { RiInformation2Line } from '@remixicon/react';
import { Trans, useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import { ContactPurchase } from '@/components/LimitAlert/ContactPurchase';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { handleGetContactLimit } from '@/store/ContactLimit/action';

export const LimitAlert: React.FC = () => {
  const { t } = useTranslation();
  const { contactLimit } = useAppSelector(getSelector('contactLimit'));
  const dispatch = useAppDispatch();

  const [openPurchase, setOpenPurchase] = useState<boolean>(false);

  const handleCheckLimit = (percent: number) => {
    if (percent < 90 && percent > 80) {
      return {
        background: '#FFFCEB',
        process: '#D98206',
      };
    } else if (percent > 90) {
      return {
        background: '#FFD9D9',
        process: '#BF1616',
      };
    } else {
      return {
        background: '#EEF9FF',
        process: '#146BE1',
      };
    }
  };

  useEffect(() => {
    dispatch(handleGetContactLimit());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const value = useMemo(() => {
    if (!contactLimit?.used || !contactLimit?.limit) {
      return 0;
    }
    return (contactLimit.used / contactLimit.limit) * 100;
  }, [contactLimit?.used, contactLimit?.limit]);

  const { background, process } = useMemo(() => handleCheckLimit(value), [value]);

  return (
    <div
      className="flex justify-between h-[56px] w-full px-4 py-3 rounded-xl mb-3 mt-2"
      style={{
        background: background,
      }}
    >
      <div className="flex gap-[10px]">
        <RiInformation2Line color={process} size={20} />
        <div className="flex flex-col gap-1">
          <span className="text-xs">
            <Trans
              id="limitAlert.description"
              name={'limitAlert.description'}
              i18nKey="limitAlert.description"
              values={{
                min: Number(100000000|| 0).toLocaleString(),
                max: Number(contactLimit.limit || 0).toLocaleString(),
                name: '',
                content: 'contacts.',
              }}
              components={{ bold: <span className="font-bold" /> }}
            />
          </span>
          <div className="w-[169px] bg-background-foreground rounded-full border border-tertiary">
            <Progress value={value} className="h-[8px] w-[169px]" color={process} />
          </div>
        </div>
      </div>

      <Modal
        className="w-[850px] max-w-none p-4"
        titleAlign={'start'}
        title={t('limitAlert.contactPurchase')}
        openModal={openPurchase}
        onOpenChange={setOpenPurchase}
        isCloseIcon={false}
        trigger={
          <Button
            variant={'textOnly'}
            size={'textOnly'}
            className="text-base"
            onClick={() => setOpenPurchase(true)}
          >
            {t('common.button.buyMore')}
          </Button>
        }
      >
        <ContactPurchase setOpenPurchase={setOpenPurchase} />
      </Modal>
    </div>
  );
};
