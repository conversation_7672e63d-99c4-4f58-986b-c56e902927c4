import { ColumnDef } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { TPhoneBought } from '@/types/contactList';
import { CONTACT_LABEL } from '@/constants/contact-list/label';
import { KEY_PHONE_BOUGHT } from '@/constants/contact-list';
import { handleFormatPhone } from '@/utils/helper';

interface IGetPhonesColumn {
  isDisabledCheckbox: boolean;
}

export const getPhonesColumn = ({ isDisabledCheckbox = false }: IGetPhonesColumn) => {
  return [
    {
      header: CONTACT_LABEL.phone_number,
      accessorKey: KEY_PHONE_BOUGHT.phone_number,
      cell: ({ row }) => {
        return <div className="flex items-center gap-3">
          <Checkbox
            disabled={isDisabledCheckbox}
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
          />
          {handleFormatPhone(row.original.phone_number)}
        </div>
      },
    },
    {
      header: () => {
        return <div className="text-center w-[189px]">{CONTACT_LABEL.switchboard}</div>;
      },
      cell: ({ row }) => (
        <div className="text-center w-[189px]">{row.original.telecom_provider}</div>
      ),
      accessorKey: KEY_PHONE_BOUGHT.telecom_provider,
      size: 189,
    },
  ] as ColumnDef<TPhoneBought>[];
};
