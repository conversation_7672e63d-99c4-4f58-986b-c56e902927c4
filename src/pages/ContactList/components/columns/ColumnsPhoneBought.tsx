import { ColumnDef } from '@tanstack/react-table';
// import StatusColumn from './StatusColumn';
import { TPhoneBought } from '@/types/contactList';
import { KEY_PHONE_BOUGHT } from '@/constants/contact-list';
import ActionColumn from './ActionColumn';
import { t } from 'i18next';
import OverDueStatus from '../OverDueStatus';
import SelectPhoneCol from '@/pages/ContactList/components/columns/SelectPhoneCol';
import { handleFormatPhone } from '@/utils/helper';
import StatusColumn from '@/pages/ContactList/components/columns/StatusColumn';

const handleCheckExpired = (data: TPhoneBought) => {
  const currentDate = new Date();
  const expiryDate = new Date(data.date_expired);
  const diffTime = expiryDate.getTime() - currentDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return {
    diffDays,
    isExpired: diffDays < 0,
  };
};

export const columnsPhoneBought = (): ColumnDef<TPhoneBought>[] => {
  return [
    {
      header: () => <div className="pl-3">{t('contactList.phoneNumber')}</div>,
      accessorKey: KEY_PHONE_BOUGHT.phone_number,
      cell: ({ row }) => (
        <div className="text-start text-lg min-w-[200px] pl-3 flex items-center gap-1">
          <SelectPhoneCol row={row} />
          {handleFormatPhone(row.original.phone_number)}
          <OverDueStatus row={row} />
        </div>
      ),
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      size: 'auto',
      maxSize: 650,
    },
    {
      header: () => <div className="text-center w-full">{t('contactList.switchboard')}</div>,
      accessorKey: KEY_PHONE_BOUGHT.telecom_provider,
      cell: ({ row }) => (
        <div className="capitalize text-sm text-center ">{row.original.telecom_provider || '-'  }</div>
      ),
      size: 144,
    },
    {
      header: () => <div className="text-center w-full">{t('contactList.status')}</div>,
      accessorKey: KEY_PHONE_BOUGHT.status,
      cell: ({ row }) => (
        <div className=" flex justify-center">
          <StatusColumn status={row.original.status} />
        </div>
      ),
      size: 144,
    },
    {
      header: () => <div className="text-center w-full">{t('contactList.timeRemaining')}</div>,
      accessorKey: 'date_created',
      cell: ({ row }) => {
        const { diffDays, isExpired } = handleCheckExpired(row.original);
        return (
          <div className="w-[160px] flex justify-center text-sm">
            {!isExpired ? `${diffDays} ${t('common.days')}` : t('common.expired')}
          </div>
        );
      },
      size: 160,
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const { isExpired } = handleCheckExpired(row.original);
        return <ActionColumn row={row} isDisabled={isExpired} />;
      },
      size: 200,
    },
  ];
};
